/**
 * 测试求助API接口的脚本
 * 用于验证前端API路由是否正确对接后端
 */

const API_BASE_URL = "http://localhost:3000/api";

// 测试获取求助列表
async function testGetHelpRequests() {
  console.log("🧪 测试获取求助列表...");

  try {
    const params = new URLSearchParams({
      page: "1",
      size: "20",
      status: "open",
      resource_type: "movie",
      search: "测试搜索",
    });

    const response = await fetch(
      `${API_BASE_URL}/help/requests?${params.toString()}`
    );
    const data = await response.json();

    console.log("✅ 获取求助列表成功:", {
      status: response.status,
      success: data.success,
      total: data.data?.total || 0,
    });

    return data;
  } catch (error) {
    console.error("❌ 获取求助列表失败:", error.message);
    return null;
  }
}

// 测试获取求助详情
async function testGetHelpRequestDetail(id = 1) {
  console.log(`🧪 测试获取求助详情 (ID: ${id})...`);

  try {
    const response = await fetch(`${API_BASE_URL}/help/requests/${id}`);
    const data = await response.json();

    console.log("✅ 获取求助详情成功:", {
      status: response.status,
      success: data.success,
      title: data.data?.help_request?.title || "N/A",
    });

    return data;
  } catch (error) {
    console.error("❌ 获取求助详情失败:", error.message);
    return null;
  }
}

// 测试创建求助 (需要认证)
async function testCreateHelpRequest(authToken) {
  console.log("🧪 测试创建求助...");

  if (!authToken) {
    console.log("⚠️  跳过创建求助测试 (需要认证token)");
    return null;
  }

  try {
    const requestData = {
      title: "测试求助标题",
      description: "这是一个测试求助的描述",
      resource_types: ["movie"],
      pan_types: [1, 2],
      tags: ["测试", "电影"],
    };

    const response = await fetch(`${API_BASE_URL}/help/requests`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      body: JSON.stringify(requestData),
    });

    const data = await response.json();

    console.log("✅ 创建求助成功:", {
      status: response.status,
      success: data.success,
      id: data.data?.id || "N/A",
    });

    return data;
  } catch (error) {
    console.error("❌ 创建求助失败:", error.message);
    return null;
  }
}

// 测试获取用户求助列表 (需要认证)
async function testGetUserHelpRequests(authToken) {
  console.log("🧪 测试获取用户求助列表...");

  if (!authToken) {
    console.log("⚠️  跳过用户求助列表测试 (需要认证token)");
    return null;
  }

  try {
    const params = new URLSearchParams({
      page: "1",
      size: "10",
    });

    const response = await fetch(
      `${API_BASE_URL}/user/help/requests?${params.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      }
    );

    const data = await response.json();

    console.log("✅ 获取用户求助列表成功:", {
      status: response.status,
      success: data.success,
      total: data.data?.total || 0,
    });

    return data;
  } catch (error) {
    console.error("❌ 获取用户求助列表失败:", error.message);
    return null;
  }
}

// 测试管理员获取求助列表 (需要管理员认证)
async function testGetAdminHelpRequests(adminToken) {
  console.log("🧪 测试管理员获取求助列表...");

  if (!adminToken) {
    console.log("⚠️  跳过管理员求助列表测试 (需要管理员token)");
    return null;
  }

  try {
    const params = new URLSearchParams({
      page: "1",
      size: "20",
      search: "测试",
      status: "all",
    });

    const response = await fetch(
      `${API_BASE_URL}/admin/help/requests?${params.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${adminToken}`,
        },
      }
    );

    const data = await response.json();

    console.log("✅ 管理员获取求助列表成功:", {
      status: response.status,
      success: data.success,
      total: data.data?.total || 0,
    });

    return data;
  } catch (error) {
    console.error("❌ 管理员获取求助列表失败:", error.message);
    return null;
  }
}

// 主测试函数
async function runTests() {
  console.log("🚀 开始测试求助API接口...\n");

  // 从命令行参数获取token (可选)
  const authToken = process.argv[2];
  const adminToken = process.argv[3];

  if (authToken) {
    console.log("🔑 使用认证token:", authToken.substring(0, 10) + "...");
  }
  if (adminToken) {
    console.log("🔑 使用管理员token:", adminToken.substring(0, 10) + "...");
  }
  console.log("");

  // 执行测试
  await testGetHelpRequests();
  console.log("");

  await testGetHelpRequestDetail();
  console.log("");

  await testCreateHelpRequest(authToken);
  console.log("");

  await testGetUserHelpRequests(authToken);
  console.log("");

  await testGetAdminHelpRequests(adminToken);
  console.log("");

  console.log("✨ 测试完成!");
  console.log("");
  console.log("使用方法:");
  console.log("  node test-help-requests-api.js [用户token] [管理员token]");
  console.log("");
  console.log("示例:");
  console.log(
    "  node test-help-requests-api.js eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  );
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testGetHelpRequests,
  testGetHelpRequestDetail,
  testCreateHelpRequest,
  testGetUserHelpRequests,
  testGetAdminHelpRequests,
};
