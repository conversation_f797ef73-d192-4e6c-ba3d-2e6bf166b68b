/**
 * 资源求助相关的API服务
 */

import { apiRequest } from "./api";
import {
  HelpRequest,
  HelpAnswer,
  HelpRequestListResponse,
  HelpRequestDetailResponse,
  CreateHelpRequestData,
  CreateAnswerData,
  HelpRequestFilters,
  AdminHelpRequestFilters,
  AdminHelpRequestListResponse,
  HelpRequestStats,
  ApiResponse,
} from "@/types/help-request";
import { UserStats, PointsRecord } from "@/types/user-level";

// const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

/**
 * 获取求助列表
 */
export async function getHelpRequests(
  page: number = 1,
  size: number = 20,
  filters?: HelpRequestFilters
): Promise<HelpRequestListResponse> {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });

    // 添加筛选参数
    if (filters) {
      if (filters.status && filters.status !== "all") {
        params.append("status", filters.status);
      }
      if (filters.pan_type) {
        params.append("pan_type", filters.pan_type.toString());
      }
      if (filters.resource_type) {
        params.append("resource_type", filters.resource_type);
      }
      if (filters.search) {
        params.append("search", filters.search);
      }
      if (filters.user_id) {
        params.append("user_id", filters.user_id.toString());
      }
    }

    return await apiRequest<HelpRequestListResponse>(
      `/api/help/requests?${params.toString()}`
    );
  } catch (error) {
    console.error("获取求助列表失败:", error);
    return {
      status: "error",
      message: "获取求助列表失败",
      data: {
        requests: [],
        total: 0,
        page: 1,
        size: 20,
        pages: 0,
      },
      error: error instanceof Error ? error.message : "获取求助列表失败",
    };
  }
}

/**
 * 获取求助详情
 */
export async function getHelpRequestDetail(
  id: number
): Promise<HelpRequestDetailResponse> {
  try {
    return await apiRequest<HelpRequestDetailResponse>(
      `/api/help/requests/${id}`
    );
  } catch (error) {
    console.error("获取求助详情失败:", error);
    return {
      success: false,
      data: {
        help_request: {} as HelpRequest,
        answers: [],
      },
      error: error instanceof Error ? error.message : "获取求助详情失败",
    };
  }
}

/**
 * 创建求助
 */
export async function createHelpRequest(
  data: CreateHelpRequestData
): Promise<ApiResponse> {
  try {
    return await apiRequest<ApiResponse>("/api/help/requests", {
      method: "POST",
      body: JSON.stringify(data),
    });
  } catch (error) {
    console.error("创建求助失败:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "创建求助失败",
    };
  }
}

/**
 * 更新求助
 */
export async function updateHelpRequest(
  id: number,
  data: Partial<CreateHelpRequestData>
): Promise<ApiResponse> {
  try {
    return await apiRequest<ApiResponse>(`/api/help/requests/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  } catch (error) {
    console.error("更新求助失败:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "更新求助失败",
    };
  }
}

/**
 * 删除求助
 */
export async function deleteHelpRequest(id: number): Promise<ApiResponse> {
  try {
    return await apiRequest<ApiResponse>(`/api/help/requests/${id}`, {
      method: "DELETE",
    });
  } catch (error) {
    console.error("删除求助失败:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "删除求助失败",
    };
  }
}

/**
 * 创建回答
 */
export async function createAnswer(
  data: CreateAnswerData
): Promise<ApiResponse> {
  try {
    return await apiRequest<ApiResponse>(
      `/api/help/requests/${data.help_request_id}/answers`,
      {
        method: "POST",
        body: JSON.stringify(data),
      }
    );
  } catch (error) {
    console.error("创建回答失败:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "创建回答失败",
    };
  }
}

/**
 * 采纳答案
 */
export async function adoptAnswer(
  helpRequestId: number,
  answerId: number
): Promise<ApiResponse> {
  try {
    return await apiRequest<ApiResponse>(
      `/api/help/requests/${helpRequestId}/adopt`,
      {
        method: "PUT",
        body: JSON.stringify({ answer_id: answerId }),
      }
    );
  } catch (error) {
    console.error("采纳答案失败:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "采纳答案失败",
    };
  }
}

/**
 * 删除回答
 */
export async function deleteAnswer(answerId: number): Promise<ApiResponse> {
  try {
    return await apiRequest<ApiResponse>(`/api/answers/${answerId}`, {
      method: "DELETE",
    });
  } catch (error) {
    console.error("删除回答失败:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "删除回答失败",
    };
  }
}

/**
 * 获取用户的求助列表
 */
export async function getUserHelpRequests(
  page: number = 1,
  size: number = 20
): Promise<HelpRequestListResponse> {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });

    return await apiRequest<HelpRequestListResponse>(
      `/api/user/help/requests?${params.toString()}`
    );
  } catch (error) {
    console.error("获取用户求助列表失败:", error);
    return {
      status: "error",
      message: "获取用户求助列表失败",
      data: {
        requests: [],
        total: 0,
        page: 1,
        size: 20,
        pages: 0,
      },
      error: error instanceof Error ? error.message : "获取用户求助列表失败",
    };
  }
}

/**
 * 获取用户的回答列表
 */
export async function getUserAnswers(
  page: number = 1,
  limit: number = 20
): Promise<{
  success: boolean;
  data: {
    answers: HelpAnswer[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
  error?: string;
}> {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    return await apiRequest(`/api/user/answers?${params.toString()}`);
  } catch (error) {
    console.error("获取用户回答列表失败:", error);
    return {
      success: false,
      data: {
        answers: [],
        total: 0,
        page: 1,
        limit: 20,
        total_pages: 0,
      },
      error: error instanceof Error ? error.message : "获取用户回答列表失败",
    };
  }
}

/**
 * 获取用户统计信息
 */
export async function getUserStats(): Promise<{
  success: boolean;
  data?: UserStats;
  error?: string;
}> {
  try {
    return await apiRequest<{ success: boolean; data: UserStats }>(
      "/api/user/stats"
    );
  } catch (error) {
    console.error("获取用户统计失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "获取用户统计失败",
    };
  }
}

/**
 * 获取用户积分记录
 */
export async function getUserPointsHistory(
  page: number = 1,
  limit: number = 20
): Promise<{
  success: boolean;
  data: {
    records: PointsRecord[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
  error?: string;
}> {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    return await apiRequest(`/api/user/points-history?${params.toString()}`);
  } catch (error) {
    console.error("获取积分记录失败:", error);
    return {
      success: false,
      data: {
        records: [],
        total: 0,
        page: 1,
        limit: 20,
        total_pages: 0,
      },
      error: error instanceof Error ? error.message : "获取积分记录失败",
    };
  }
}

// ==================== 管理员相关API ====================

/**
 * 管理员获取求助列表
 */
export async function getAdminHelpRequests(
  page: number = 1,
  size: number = 20,
  filters?: AdminHelpRequestFilters
): Promise<AdminHelpRequestListResponse> {
  try {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });

    // 添加筛选参数
    if (filters) {
      if (filters.search) {
        params.append("search", filters.search);
      }
      if (filters.status && filters.status !== "all") {
        params.append("status", filters.status);
      }
      if (filters.pan_type) {
        params.append("pan_type", filters.pan_type.toString());
      }
      if (filters.resource_type) {
        params.append("resource_type", filters.resource_type);
      }
      if (filters.user_id) {
        params.append("user_id", filters.user_id.toString());
      }
    }

    return await apiRequest<AdminHelpRequestListResponse>(
      `/api/admin/help/requests?${params.toString()}`
    );
  } catch (error) {
    console.error("管理员获取求助列表失败:", error);
    return {
      success: false,
      data: {
        help_requests: [],
        total: 0,
        page: 1,
        limit: 20,
        total_pages: 0,
      },
      error: error instanceof Error ? error.message : "获取求助列表失败",
    };
  }
}

/**
 * 管理员删除求助
 */
export async function adminDeleteHelpRequest(id: number): Promise<ApiResponse> {
  try {
    return await apiRequest<ApiResponse>(`/api/admin/help/requests/${id}`, {
      method: "DELETE",
    });
  } catch (error) {
    console.error("管理员删除求助失败:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "删除求助失败",
    };
  }
}

/**
 * 管理员批量删除求助
 */
export async function adminBatchDeleteHelpRequests(
  ids: number[]
): Promise<ApiResponse> {
  try {
    return await apiRequest<ApiResponse>(
      "/api/admin/help/requests/batch-delete",
      {
        method: "POST",
        body: JSON.stringify({ ids }),
      }
    );
  } catch (error) {
    console.error("管理员批量删除求助失败:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "批量删除求助失败",
    };
  }
}

/**
 * 获取求助统计信息
 */
export async function getHelpRequestStats(): Promise<{
  success: boolean;
  data?: HelpRequestStats;
  error?: string;
}> {
  try {
    return await apiRequest<{ success: boolean; data: HelpRequestStats }>(
      "/api/admin/help/requests/stats"
    );
  } catch (error) {
    console.error("获取求助统计失败:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "获取求助统计失败",
    };
  }
}
