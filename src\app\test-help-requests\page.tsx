"use client";

import { useState, useEffect } from "react";
import { getHelpRequests } from "@/services/helpRequestService";
import { HelpRequestFilters } from "@/types/help-request";

export default function TestHelpRequestsPage() {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<HelpRequestFilters>({
    status: "all",
  });

  const testAPI = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log("测试参数:", {
        page: 1,
        size: 20,
        filters,
      });

      const response = await getHelpRequests(1, 20, filters);
      console.log("API响应:", response);
      setResults(response);
    } catch (err) {
      console.error("API调用失败:", err);
      setError(err instanceof Error ? err.message : "未知错误");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testAPI();
  }, []);

  const handleFilterChange = (key: keyof HelpRequestFilters, value: any) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleTest = () => {
    testAPI();
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">求助API接口测试</h1>

      {/* 测试参数 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">测试参数</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-2">状态</label>
            <select
              value={filters.status || "all"}
              onChange={(e) => handleFilterChange("status", e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              title="选择状态筛选条件"
            >
              <option value="all">全部状态</option>
              <option value="open">求助中</option>
              <option value="resolved">已解决</option>
              <option value="closed">已关闭</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">资源类型</label>
            <select
              value={filters.resource_type || ""}
              onChange={(e) =>
                handleFilterChange("resource_type", e.target.value || undefined)
              }
              className="w-full px-3 py-2 border rounded-md"
              title="选择资源类型筛选条件"
            >
              <option value="">全部类型</option>
              <option value="movie">电影</option>
              <option value="tv">电视剧</option>
              <option value="music">音乐</option>
              <option value="software">软件</option>
              <option value="game">游戏</option>
              <option value="book">书籍</option>
              <option value="document">文档</option>
              <option value="other">其他</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">搜索关键词</label>
            <input
              type="text"
              value={filters.search || ""}
              onChange={(e) =>
                handleFilterChange("search", e.target.value || undefined)
              }
              placeholder="输入搜索关键词"
              className="w-full px-3 py-2 border rounded-md"
            />
          </div>
        </div>

        <button
          type="button"
          onClick={handleTest}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? "测试中..." : "测试API"}
        </button>
      </div>

      {/* 测试结果 */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-xl font-semibold mb-4">测试结果</h2>

        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">正在测试API...</p>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
            <h3 className="text-red-800 font-medium">错误信息</h3>
            <p className="text-red-600 mt-1">{error}</p>
          </div>
        )}

        {results && (
          <div className="space-y-4">
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <h3 className="text-green-800 font-medium">API调用成功</h3>
              <div className="mt-2 text-sm text-green-600">
                <p>
                  响应状态:{" "}
                  {results.status === "success" ? "✅ 成功" : "❌ 失败"}
                </p>
                <p>响应消息: {results.message}</p>
                {results.data && (
                  <>
                    <p>总数量: {results.data.total || 0}</p>
                    <p>当前页: {results.data.page || 1}</p>
                    <p>每页数量: {results.data.size || 20}</p>
                    <p>总页数: {results.data.pages || 0}</p>
                    <p>求助数量: {results.data.requests?.length || 0}</p>
                  </>
                )}
              </div>
            </div>

            <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
              <h3 className="font-medium mb-2">完整响应数据</h3>
              <pre className="text-xs bg-gray-100 p-3 rounded overflow-auto max-h-96">
                {JSON.stringify(results, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>

      {/* 使用说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mt-6">
        <h3 className="text-blue-800 font-medium mb-2">使用说明</h3>
        <div className="text-sm text-blue-600 space-y-1">
          <p>• 这个页面用于测试求助API接口是否正常工作</p>
          <p>• 修改上方的测试参数，然后点击"测试API"按钮</p>
          <p>• 查看下方的测试结果，确认API返回的数据格式是否正确</p>
          <p>• 打开浏览器开发者工具查看详细的网络请求和响应</p>
        </div>
      </div>
    </div>
  );
}
