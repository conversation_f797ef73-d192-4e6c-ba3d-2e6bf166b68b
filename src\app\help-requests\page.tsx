"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { PlusIcon, MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/components/ToastProvider";
import { getHelpRequests } from "@/services/helpRequestService";
import {
  type HelpRequest,
  type HelpRequestFilters,
  PAN_TYPE_MAP,
  RESOURCE_TYPES,
} from "@/types/help-request";
import Pagination from "@/components/Pagination";

// 求助卡片组件 - 论坛风格
function HelpRequestCard({ helpRequest }: { helpRequest: HelpRequest }) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-green-500";
      case "resolved":
        return "bg-blue-500";
      case "closed":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "open":
        return "求助中";
      case "resolved":
        return "已解决";
      case "closed":
        return "已关闭";
      default:
        return "未知";
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors">
      <div className="flex items-start gap-4 p-4">
        {/* 状态指示器 */}
        <div className="flex-shrink-0 mt-1">
          <div
            className={`w-3 h-3 rounded-full ${getStatusColor(
              helpRequest.status
            )}`}
          ></div>
        </div>

        {/* 用户头像占位 */}
        <div className="flex-shrink-0">
          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
        </div>

        {/* 主要内容 */}
        <div className="flex-1 min-w-0">
          {/* 标题和标签行 */}
          <div className="flex items-start justify-between mb-2">
            <div className="flex-1 min-w-0 pr-3">
              <div className="flex items-start flex-wrap gap-2">
                <Link
                  href={`/help-requests/${helpRequest.id}`}
                  className="text-base font-medium text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                >
                  {helpRequest.title}
                </Link>
                {/* 标签移到标题同行 */}
                {helpRequest.resource_type && (
                  <span className="px-2 py-0.5 bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300 rounded text-xs font-medium whitespace-nowrap">
                    {helpRequest.resource_type === "movie"
                      ? "电影"
                      : helpRequest.resource_type === "tv"
                      ? "电视剧"
                      : helpRequest.resource_type === "music"
                      ? "音乐"
                      : helpRequest.resource_type === "software"
                      ? "软件"
                      : helpRequest.resource_type === "game"
                      ? "游戏"
                      : helpRequest.resource_type === "book"
                      ? "书籍"
                      : helpRequest.resource_type === "document"
                      ? "文档"
                      : helpRequest.resource_type === "other"
                      ? "其他"
                      : helpRequest.resource_type}
                  </span>
                )}
                {helpRequest.cloud_disk_types?.map((diskType) => (
                  <span
                    key={diskType}
                    className="px-2 py-0.5 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300 rounded text-xs font-medium whitespace-nowrap"
                  >
                    {diskType === "baidu"
                      ? "百度网盘"
                      : diskType === "aliyun"
                      ? "阿里云盘"
                      : diskType === "quark"
                      ? "夸克网盘"
                      : diskType === "xunlei"
                      ? "迅雷网盘"
                      : diskType}
                  </span>
                ))}
              </div>
            </div>
            <span
              className={`px-2 py-1 text-xs font-medium text-white rounded ${getStatusColor(
                helpRequest.status
              )} whitespace-nowrap flex-shrink-0`}
            >
              {getStatusText(helpRequest.status)}
            </span>
          </div>

          {/* 用户信息、时间和统计信息合并行 */}
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 flex-wrap gap-x-3 gap-y-1 mb-2">
            <span className="font-medium text-blue-600 dark:text-blue-400 hover:underline cursor-pointer">
              {helpRequest.requester.nickname || helpRequest.requester.username}
            </span>
            <span>•</span>
            <span>{formatDateTime(helpRequest.created_at)}</span>
            <span>•</span>
            <span className="flex items-center">
              👁 {helpRequest.view_count}
            </span>
            <span>•</span>
            <span className="flex items-center">
              💬 {helpRequest.answer_count}
            </span>
          </div>

          {/* 描述 */}
          {helpRequest.description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
              {helpRequest.description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

// 筛选器组件
function HelpRequestFilters({
  filters,
  onFiltersChange,
}: {
  filters: HelpRequestFilters;
  onFiltersChange: (filters: HelpRequestFilters) => void;
}) {
  const [searchQuery, setSearchQuery] = useState("");

  const handleFilterChange = (key: keyof HelpRequestFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const handleSearch = () => {
    onFiltersChange({
      ...filters,
      search: searchQuery || undefined,
    });
  };

  return (
    <div className="bg-card-background rounded-lg p-4 mb-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
        {/* 状态筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            状态
          </label>
          <select
            value={filters.status || "all"}
            onChange={(e) => handleFilterChange("status", e.target.value)}
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择状态筛选条件"
          >
            <option value="all">全部状态</option>
            <option value="open">求助中</option>
            <option value="resolved">已解决</option>
            <option value="closed">已关闭</option>
          </select>
        </div>

        {/* 资源类型筛选 */}
        <div>
          <label className="block text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
            资源类型
          </label>
          <select
            value={filters.resource_type || ""}
            onChange={(e) =>
              handleFilterChange("resource_type", e.target.value || undefined)
            }
            className="w-full px-3 py-2 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            title="选择资源类型筛选条件"
          >
            <option value="">全部类型</option>
            <option value="movie">电影</option>
            <option value="tv">电视剧</option>
            <option value="music">音乐</option>
            <option value="software">软件</option>
            <option value="game">游戏</option>
            <option value="book">书籍</option>
            <option value="document">文档</option>
            <option value="other">其他</option>
          </select>
        </div>
      </div>

      {/* 搜索框 */}
      <div className="flex gap-2">
        <div className="flex-1 relative">
          <input
            type="text"
            placeholder="搜索求助标题或描述..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleSearch()}
            className="w-full px-3 py-2 pl-10 border border-border-color rounded-md bg-background text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <MagnifyingGlassIcon className="h-5 w-5 text-secondary-text absolute left-3 top-1/2 transform -translate-y-1/2" />
        </div>
        <button
          type="button"
          onClick={handleSearch}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          搜索
        </button>
      </div>
    </div>
  );
}

export default function HelpRequestsPage() {
  const { isAuthenticated } = useAuth();
  const { showToast } = useToast();
  const [helpRequests, setHelpRequests] = useState<HelpRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState<HelpRequestFilters>({
    status: "all",
  });

  const pageSize = 20;

  const loadHelpRequests = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getHelpRequests(currentPage, pageSize, filters);
      if (response.status === "success") {
        setHelpRequests(response.data.requests);
        setTotalPages(response.data.pages);
        setTotal(response.data.total);
      } else {
        showToast(
          response.error || response.message || "获取求助列表失败",
          "error"
        );
      }
    } catch (error) {
      console.error("获取求助列表失败:", error);
      showToast("获取求助列表失败", "error");
    } finally {
      setLoading(false);
    }
  }, [currentPage, filters, showToast]);

  useEffect(() => {
    loadHelpRequests();
  }, [loadHelpRequests]);

  const handleFiltersChange = (newFilters: HelpRequestFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // 重置到第一页
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-6 bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
        <div>
          <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100">
            资源求助
          </h1>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            向社区求助，找到您需要的网盘资源
          </p>
        </div>
        {isAuthenticated && (
          <Link
            href="/help-requests/create"
            className="inline-flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            发布求助
          </Link>
        )}
      </div>

      {/* 筛选器 */}
      <HelpRequestFilters
        filters={filters}
        onFiltersChange={handleFiltersChange}
      />

      {/* 统计信息 */}
      {!loading && (
        <div className="mb-4 px-4 py-2 bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            共找到{" "}
            <span className="font-semibold text-gray-900 dark:text-gray-100">
              {total}
            </span>{" "}
            个求助
          </div>
        </div>
      )}

      {/* 求助列表 - 论坛风格 */}
      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-secondary-text">加载中...</span>
        </div>
      ) : helpRequests.length > 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          {helpRequests.map((helpRequest, index) => (
            <div key={helpRequest.id}>
              <HelpRequestCard helpRequest={helpRequest} />
              {index < helpRequests.length - 1 && (
                <hr className="border-gray-200 dark:border-gray-700" />
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <p className="text-secondary-text">暂无求助信息</p>
          {isAuthenticated && (
            <Link
              href="/help-requests/create"
              className="inline-flex items-center px-4 py-2 mt-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <PlusIcon className="h-5 w-5 mr-2" />
              发布第一个求助
            </Link>
          )}
        </div>
      )}

      {/* 分页 */}
      {!loading && totalPages > 1 && (
        <div className="mt-8">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}
