'use client';

import { useState, useEffect } from 'react';
import { getHelpRequests } from '@/services/helpRequestService';
import { HelpRequest, HelpRequestFilters } from '@/types/help-request';

// 网盘类型映射
const CLOUD_DISK_MAP: { [key: string]: string } = {
  'baidu': '百度网盘',
  'aliyun': '阿里云盘',
  'quark': '夸克网盘',
  'xunlei': '迅雷网盘',
};

// 资源类型映射
const RESOURCE_TYPE_MAP: { [key: string]: string } = {
  'movie': '电影',
  'tv': '电视剧',
  'music': '音乐',
  'software': '软件',
  'game': '游戏',
  'book': '书籍',
  'document': '文档',
  'other': '其他',
};

// 状态映射
const STATUS_MAP: { [key: string]: { label: string; color: string } } = {
  'open': { label: '求助中', color: 'bg-blue-100 text-blue-800' },
  'resolved': { label: '已解决', color: 'bg-green-100 text-green-800' },
  'closed': { label: '已关闭', color: 'bg-gray-100 text-gray-800' },
};

export default function HelpRequestsDemoPage() {
  const [requests, setRequests] = useState<HelpRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    size: 20,
    pages: 0,
  });

  const [filters, setFilters] = useState<HelpRequestFilters>({
    status: 'all',
  });

  const fetchRequests = async (page = 1) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getHelpRequests(page, 20, filters);
      
      if (response.status === 'success') {
        setRequests(response.data.requests);
        setPagination({
          total: response.data.total,
          page: response.data.page,
          size: response.data.size,
          pages: response.data.pages,
        });
      } else {
        setError(response.error || '获取求助列表失败');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '网络错误');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, [filters]);

  const handleFilterChange = (key: keyof HelpRequestFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const handlePageChange = (newPage: number) => {
    fetchRequests(newPage);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">求助列表展示</h1>
      
      {/* 筛选器 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">筛选条件</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-2">状态</label>
            <select
              value={filters.status || 'all'}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              title="选择状态筛选条件"
            >
              <option value="all">全部状态</option>
              <option value="open">求助中</option>
              <option value="resolved">已解决</option>
              <option value="closed">已关闭</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">资源类型</label>
            <select
              value={filters.resource_type || ''}
              onChange={(e) => handleFilterChange('resource_type', e.target.value || undefined)}
              className="w-full px-3 py-2 border rounded-md"
              title="选择资源类型筛选条件"
            >
              <option value="">全部类型</option>
              <option value="movie">电影</option>
              <option value="tv">电视剧</option>
              <option value="music">音乐</option>
              <option value="software">软件</option>
              <option value="game">游戏</option>
              <option value="book">书籍</option>
              <option value="document">文档</option>
              <option value="other">其他</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">搜索关键词</label>
            <input
              type="text"
              value={filters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value || undefined)}
              placeholder="输入搜索关键词"
              className="w-full px-3 py-2 border rounded-md"
            />
          </div>
        </div>
        
        <button
          type="button"
          onClick={() => fetchRequests(1)}
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? '搜索中...' : '搜索'}
        </button>
      </div>

      {/* 统计信息 */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">
            共找到 <span className="font-semibold text-blue-600">{pagination.total}</span> 条求助
          </span>
          <span className="text-gray-600">
            第 {pagination.page} 页，共 {pagination.pages} 页
          </span>
        </div>
      </div>

      {/* 加载状态 */}
      {loading && (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600">正在加载求助列表...</p>
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <h3 className="text-red-800 font-medium">加载失败</h3>
          <p className="text-red-600 mt-1">{error}</p>
          <button
            onClick={() => fetchRequests()}
            className="mt-2 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
          >
            重试
          </button>
        </div>
      )}

      {/* 求助列表 */}
      {!loading && !error && (
        <div className="space-y-4">
          {requests.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无求助数据
            </div>
          ) : (
            requests.map((request) => (
              <div key={request.id} className="bg-white rounded-lg shadow p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {request.title}
                    </h3>
                    {request.description && (
                      <p className="text-gray-600 mb-3">{request.description}</p>
                    )}
                  </div>
                  <div className="ml-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${STATUS_MAP[request.status]?.color || 'bg-gray-100 text-gray-800'}`}>
                      {STATUS_MAP[request.status]?.label || request.status}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                  <div>
                    <span className="text-sm text-gray-500">资源类型：</span>
                    <span className="text-sm font-medium">
                      {RESOURCE_TYPE_MAP[request.resource_type] || request.resource_type}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">网盘类型：</span>
                    <span className="text-sm font-medium">
                      {request.cloud_disk_types.map(type => CLOUD_DISK_MAP[type] || type).join(', ')}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">回答数：</span>
                    <span className="text-sm font-medium">{request.answer_count}</span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">浏览数：</span>
                    <span className="text-sm font-medium">{request.view_count}</span>
                  </div>
                </div>

                <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-4">
                    <div>
                      <span className="text-sm text-gray-500">求助者：</span>
                      <span className="text-sm font-medium">{request.requester.nickname || request.requester.username}</span>
                      <span className="text-xs text-gray-400 ml-1">({request.requester.points} 积分)</span>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">发布时间：</span>
                      <span className="text-sm">{formatDate(request.created_at)}</span>
                    </div>
                    {request.resolved_at && (
                      <div>
                        <span className="text-sm text-gray-500">解决时间：</span>
                        <span className="text-sm">{formatDate(request.resolved_at)}</span>
                      </div>
                    )}
                  </div>
                  <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                    查看详情
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* 分页 */}
      {!loading && !error && pagination.pages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex space-x-2">
            <button
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page <= 1}
              className="px-3 py-2 border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              上一页
            </button>
            
            {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
              const pageNum = i + 1;
              return (
                <button
                  key={pageNum}
                  onClick={() => handlePageChange(pageNum)}
                  className={`px-3 py-2 border rounded-md ${
                    pageNum === pagination.page
                      ? 'bg-blue-600 text-white border-blue-600'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}
            
            <button
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page >= pagination.pages}
              className="px-3 py-2 border rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
            >
              下一页
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
