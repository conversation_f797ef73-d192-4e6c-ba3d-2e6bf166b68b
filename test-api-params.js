/**
 * 测试API参数的脚本
 * 验证前端发送的参数是否符合后端规范
 */

const API_BASE_URL = "http://localhost:3001/api";

// 测试参数组合
const testCases = [
  {
    name: "基础参数测试",
    params: {
      page: 1,
      size: 20,
    },
  },
  {
    name: "状态筛选测试",
    params: {
      page: 1,
      size: 10,
      status: "open",
    },
  },
  {
    name: "资源类型筛选测试",
    params: {
      page: 1,
      size: 15,
      resource_type: "movie",
    },
  },
  {
    name: "搜索功能测试",
    params: {
      page: 1,
      size: 20,
      search: "测试搜索关键词",
    },
  },
  {
    name: "组合筛选测试",
    params: {
      page: 2,
      size: 25,
      status: "resolved",
      resource_type: "tv",
      search: "电视剧",
    },
  },
  {
    name: "边界值测试",
    params: {
      page: 1,
      size: 100, // 最大值
      status: "closed",
    },
  },
];

async function testAPIParams() {
  console.log("🧪 开始测试API参数...\n");

  for (const testCase of testCases) {
    console.log(`📋 ${testCase.name}`);

    // 构建查询参数
    const params = new URLSearchParams();
    Object.entries(testCase.params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });

    const url = `${API_BASE_URL}/help/requests?${params.toString()}`;
    console.log(`🔗 请求URL: ${url}`);

    try {
      const response = await fetch(url);
      const data = await response.json();

      console.log(`✅ 状态码: ${response.status}`);
      console.log(`📊 响应: ${data.success ? "成功" : "失败"}`);

      if (data.data) {
        console.log(
          `📈 数据: 总数=${data.data.total || 0}, 页码=${
            data.data.page || 1
          }, 每页=${data.data.limit || 20}`
        );
      }

      if (data.error) {
        console.log(`❌ 错误: ${data.error}`);
      }
    } catch (error) {
      console.log(`❌ 请求失败: ${error.message}`);
    }

    console.log(""); // 空行分隔
  }

  console.log("✨ 参数测试完成!\n");
}

// 验证参数格式
function validateParams() {
  console.log("🔍 验证参数格式...\n");

  const validStatuses = ["open", "resolved", "closed"];
  const validResourceTypes = [
    "movie",
    "tv",
    "music",
    "software",
    "game",
    "book",
    "document",
    "other",
  ];

  console.log("✅ 支持的状态值:", validStatuses.join(", "));
  console.log("✅ 支持的资源类型:", validResourceTypes.join(", "));
  console.log("✅ 页码范围: >= 1");
  console.log("✅ 每页数量范围: 1-100");
  console.log("✅ 搜索关键词: 任意字符串");
  console.log("");
}

// 检查是否移除了不支持的参数
function checkRemovedParams() {
  console.log("🚫 已移除的不支持参数...\n");

  const removedParams = ["sort_by", "limit"];

  removedParams.forEach((param) => {
    console.log(`❌ ${param} - 已从API中移除`);
  });

  console.log("");
  console.log("📝 说明:");
  console.log("  • sort_by: 后端不支持排序参数，排序由后端自动处理");
  console.log("  • limit: 已改为 size 参数以符合后端规范");
  console.log("");
}

// 主函数
async function main() {
  console.log("🎯 求助API参数测试工具\n");
  console.log("=".repeat(50));
  console.log("");

  validateParams();
  checkRemovedParams();
  await testAPIParams();

  console.log("=".repeat(50));
  console.log("🎉 所有测试完成!");
  console.log("");
  console.log("💡 提示:");
  console.log("  • 如果看到网络错误，请确保后端服务正在运行");
  console.log("  • 如果看到404错误，请检查API路由配置");
  console.log("  • 如果参数错误，请检查后端API规范");
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testAPIParams,
  validateParams,
  checkRemovedParams,
};
