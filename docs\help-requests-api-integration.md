# 求助 API 接口联调文档

## 概述

本文档描述了前端与后端求助 API 接口的联调实现，包括参数映射、路由配置和测试方法。

## API 接口规范

根据后端提供的 API 规范，求助接口支持以下参数：

### 获取求助列表 `GET /api/help-requests`

**查询参数：**

- `page` (integer, >= 1, 默认: 1) - 页码
- `size` (integer, 1-100, 默认: 20) - 每页数量
- `status` (string, 可选) - 状态筛选：open(开放中)、resolved(已解决)、closed(已关闭)
- `resource_type` (string, 可选) - 资源类型筛选：movie(电影)、tv(电视剧)、music(音乐)、software(软件)、game(游戏)、book(书籍)、document(文档)、other(其他)
- `search` (string, 可选) - 搜索关键词（标题和描述）

**注意：** 后端 API 不支持 `sort_by` 参数，排序由后端自动处理。

## 前端实现

### 1. 参数映射修复

**修改前：**

```typescript
// 使用 limit 参数（不符合后端规范）
const params = new URLSearchParams({
  page: page.toString(),
  limit: limit.toString(), // ❌ 后端期望 size
});
```

**修改后：**

```typescript
// 使用 size 参数（符合后端规范）
const params = new URLSearchParams({
  page: page.toString(),
  size: size.toString(), // ✅ 符合后端规范
});
```

### 2. 搜索功能支持

**类型定义更新：**

```typescript
export interface HelpRequestFilters {
  status?: "all" | HelpRequestStatus;
  pan_type?: number;
  resource_type?: string;
  search?: string; // ✅ 新增搜索参数
  user_id?: number; // 用于筛选特定用户的求助
}
```

**移除了不支持的字段：**

- ❌ `sort_by` - 后端不支持此参数，排序由后端自动处理

**服务函数更新：**

```typescript
// 添加搜索参数支持
if (filters.search) {
  params.append("search", filters.search);
}
```

### 3. API 路由创建

创建了以下前端 API 路由文件：

- `src/app/api/help-requests/route.ts` - 求助列表和创建
- `src/app/api/help-requests/[id]/route.ts` - 求助详情、更新、删除
- `src/app/api/user/help-requests/route.ts` - 用户求助列表
- `src/app/api/admin/help-requests/route.ts` - 管理员求助管理
- `src/app/api/admin/help-requests/[id]/route.ts` - 管理员删除求助

### 4. 筛选器组件更新

**修复搜索功能：**

```typescript
const handleSearch = () => {
  // 将搜索关键词添加到筛选器中
  onFiltersChange({
    ...filters,
    search: searchQuery,
  });

  if (onSearch) {
    onSearch();
  }
};
```

## API 路由配置

### 主要接口

| 方法   | 路径                      | 描述         | 认证要求 |
| ------ | ------------------------- | ------------ | -------- |
| GET    | `/api/help-requests`      | 获取求助列表 | 无       |
| POST   | `/api/help-requests`      | 创建求助     | 需要登录 |
| GET    | `/api/help-requests/[id]` | 获取求助详情 | 无       |
| PUT    | `/api/help-requests/[id]` | 更新求助     | 需要登录 |
| DELETE | `/api/help-requests/[id]` | 删除求助     | 需要登录 |

### 用户接口

| 方法 | 路径                      | 描述             | 认证要求 |
| ---- | ------------------------- | ---------------- | -------- |
| GET  | `/api/user/help-requests` | 获取用户求助列表 | 需要登录 |

### 管理员接口

| 方法   | 路径                                    | 描述               | 认证要求       |
| ------ | --------------------------------------- | ------------------ | -------------- |
| GET    | `/api/admin/help-requests`              | 管理员获取求助列表 | 需要管理员权限 |
| DELETE | `/api/admin/help-requests/[id]`         | 管理员删除求助     | 需要管理员权限 |
| POST   | `/api/admin/help-requests/batch-delete` | 批量删除求助       | 需要管理员权限 |

## 测试方法

### 1. 测试页面

访问 `http://localhost:3001/test-help-requests` 进行可视化测试。

### 2. 命令行测试

使用提供的测试脚本：

```bash
# 基础测试（无需认证）
node test-help-requests-api.js

# 带认证测试
node test-help-requests-api.js "your_auth_token" "admin_token"
```

### 3. 手动测试

**测试获取求助列表：**

```bash
curl "http://localhost:3001/api/help-requests?page=1&size=20&status=open&resource_type=movie&search=测试"
```

**测试创建求助（需要认证）：**

```bash
curl -X POST "http://localhost:3001/api/help-requests" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "title": "测试求助",
    "description": "这是一个测试",
    "resource_types": ["movie"],
    "pan_types": [1, 2]
  }'
```

## 环境变量配置

确保设置正确的后端 API 地址：

```env
# .env.local
NEXT_PUBLIC_API_BASE_URL=http://your-backend-api-url
```

## 注意事项

1. **参数名称**：前端使用 `size` 而不是 `limit` 参数
2. **搜索功能**：支持通过 `search` 参数搜索标题和描述
3. **认证**：需要认证的接口必须在请求头中包含 `Authorization: Bearer token`
4. **错误处理**：所有 API 路由都包含完整的错误处理和状态码传递
5. **CORS**：前端 API 路由会自动处理跨域问题

## 下一步

1. 测试所有 API 接口的功能
2. 验证参数传递是否正确
3. 确认错误处理是否完善
4. 检查认证机制是否正常工作
5. 优化性能和用户体验

## 故障排除

### 常见问题

1. **404 错误**：检查 API 路由文件是否正确创建
2. **参数错误**：确认使用 `size` 而不是 `limit`
3. **认证失败**：检查 token 格式和有效性
4. **CORS 错误**：确认后端 API 地址配置正确

### 调试方法

1. 打开浏览器开发者工具查看网络请求
2. 检查控制台错误信息
3. 使用测试页面进行可视化调试
4. 查看服务器日志确认请求是否到达后端
