import Link from "next/link";

export default function DemoPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">求助功能演示</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* 求助列表展示 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">求助列表展示</h2>
          <p className="text-gray-600 mb-4">
            展示从后端API获取的求助数据，包括筛选、分页等功能。
          </p>
          <div className="space-y-2 mb-4">
            <div className="text-sm text-gray-500">✅ 数据展示</div>
            <div className="text-sm text-gray-500">✅ 状态筛选</div>
            <div className="text-sm text-gray-500">✅ 资源类型筛选</div>
            <div className="text-sm text-gray-500">✅ 搜索功能</div>
            <div className="text-sm text-gray-500">✅ 分页功能</div>
          </div>
          <Link
            href="/help-requests-demo"
            className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            查看演示
          </Link>
        </div>

        {/* API测试页面 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">API接口测试</h2>
          <p className="text-gray-600 mb-4">
            测试求助API接口的参数和响应，用于调试和验证。
          </p>
          <div className="space-y-2 mb-4">
            <div className="text-sm text-gray-500">✅ 参数测试</div>
            <div className="text-sm text-gray-500">✅ 响应验证</div>
            <div className="text-sm text-gray-500">✅ 错误处理</div>
            <div className="text-sm text-gray-500">✅ 实时调试</div>
          </div>
          <Link
            href="/test-help-requests"
            className="inline-block px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            测试接口
          </Link>
        </div>

        {/* API对接测试 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">API对接测试</h2>
          <p className="text-gray-600 mb-4">
            完整的API接口对接验证，包括所有参数组合和响应验证。
          </p>
          <div className="space-y-2 mb-4">
            <div className="text-sm text-gray-500">✅ 接口连通性测试</div>
            <div className="text-sm text-gray-500">✅ 参数组合验证</div>
            <div className="text-sm text-gray-500">✅ 响应结构检查</div>
            <div className="text-sm text-gray-500">✅ 性能监控</div>
            <div className="text-sm text-gray-500">✅ 错误处理验证</div>
          </div>
          <Link
            href="/api-integration-test"
            className="inline-block px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700"
          >
            开始测试
          </Link>
        </div>
      </div>

      {/* API响应示例 */}
      <div className="bg-white rounded-lg shadow p-6 mt-8">
        <h2 className="text-xl font-semibold mb-4">API响应示例</h2>
        <p className="text-gray-600 mb-4">以下是后端API返回的实际数据格式：</p>
        <pre className="bg-gray-100 p-4 rounded-md overflow-auto text-sm">
          {`{
  "status": "success",
  "message": "获取求助列表成功",
  "data": {
    "total": 3,
    "page": 1,
    "size": 20,
    "pages": 1,
    "requests": [
      {
        "id": 3,
        "title": "状态流转测试求助",
        "description": "这是一个用于测试状态流转的求助",
        "cloud_disk_types": ["baidu"],
        "resource_type": "movie",
        "status": "resolved",
        "requester": {
          "id": 24,
          "username": "testuser",
          "nickname": "测试用户",
          "points": 21,
          "title": "新手"
        },
        "answer_count": 1,
        "view_count": 0,
        "created_at": "2025-07-29T02:40:22.196396+00:00",
        "updated_at": "2025-07-29T02:40:22.232349+00:00",
        "resolved_at": "2025-07-28T18:40:22.232349+00:00"
      }
    ]
  }
}`}
        </pre>
      </div>

      {/* 技术说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-6 mt-8">
        <h3 className="text-blue-800 font-medium mb-2">技术说明</h3>
        <div className="text-sm text-blue-600 space-y-1">
          <p>• 前端使用 Next.js 14 + TypeScript + Tailwind CSS</p>
          <p>• API路径已修正为: /api/help/requests</p>
          <p>• 支持的参数: page, size, status, resource_type, search</p>
          <p>• 响应格式已适配实际后端API</p>
          <p>• 包含完整的错误处理和加载状态</p>
        </div>
      </div>

      {/* 使用说明 */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-6 mt-6">
        <h3 className="text-yellow-800 font-medium mb-2">使用说明</h3>
        <div className="text-sm text-yellow-600 space-y-1">
          <p>1. 确保后端服务运行在 http://127.0.0.1:9999</p>
          <p>2. 前端开发服务器运行在 http://localhost:3001</p>
          <p>3. 点击上方按钮访问不同的演示页面</p>
          <p>4. 在演示页面中可以测试筛选、搜索、分页等功能</p>
        </div>
      </div>
    </div>
  );
}
