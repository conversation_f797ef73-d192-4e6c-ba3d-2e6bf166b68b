'use client';

import { useState } from 'react';
import { getHelpRequests } from '@/services/helpRequestService';
import { HelpRequestFilters } from '@/types/help-request';

export default function ApiIntegrationTestPage() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    console.log(`🧪 开始测试: ${testName}`);
    const startTime = Date.now();
    
    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      const testResult = {
        name: testName,
        status: 'success',
        duration,
        result,
        timestamp: new Date().toLocaleString()
      };
      
      setTestResults(prev => [...prev, testResult]);
      console.log(`✅ ${testName} 成功`, result);
      return testResult;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      const testResult = {
        name: testName,
        status: 'error',
        duration,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toLocaleString()
      };
      
      setTestResults(prev => [...prev, testResult]);
      console.error(`❌ ${testName} 失败`, error);
      return testResult;
    }
  };

  const runAllTests = async () => {
    setLoading(true);
    setTestResults([]);

    // 测试1: 基础获取求助列表
    await runTest('基础获取求助列表', async () => {
      return await getHelpRequests(1, 20);
    });

    // 测试2: 带状态筛选
    await runTest('状态筛选 - 求助中', async () => {
      return await getHelpRequests(1, 20, { status: 'open' });
    });

    // 测试3: 带状态筛选 - 已解决
    await runTest('状态筛选 - 已解决', async () => {
      return await getHelpRequests(1, 20, { status: 'resolved' });
    });

    // 测试4: 资源类型筛选
    await runTest('资源类型筛选 - 电影', async () => {
      return await getHelpRequests(1, 20, { resource_type: 'movie' });
    });

    // 测试5: 搜索功能
    await runTest('搜索功能', async () => {
      return await getHelpRequests(1, 20, { search: '阿凡达' });
    });

    // 测试6: 组合筛选
    await runTest('组合筛选', async () => {
      return await getHelpRequests(1, 20, { 
        status: 'resolved', 
        resource_type: 'movie',
        search: '测试'
      });
    });

    // 测试7: 分页测试
    await runTest('分页测试 - 第2页', async () => {
      return await getHelpRequests(2, 10);
    });

    // 测试8: 边界值测试
    await runTest('边界值测试 - 最大页面大小', async () => {
      return await getHelpRequests(1, 100);
    });

    setLoading(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">API接口对接测试</h1>
      
      {/* 控制面板 */}
      <div className="bg-white rounded-lg shadow p-6 mb-6">
        <div className="flex space-x-4">
          <button
            onClick={runAllTests}
            disabled={loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? '测试中...' : '运行所有测试'}
          </button>
          <button
            onClick={clearResults}
            className="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            清空结果
          </button>
        </div>
      </div>

      {/* 测试进度 */}
      {loading && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600 mr-3"></div>
            <span className="text-blue-800">正在运行API接口测试...</span>
          </div>
        </div>
      )}

      {/* 测试结果统计 */}
      {testResults.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">测试结果统计</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-green-50 border border-green-200 rounded-md p-4">
              <div className="text-2xl font-bold text-green-800">
                {testResults.filter(r => r.status === 'success').length}
              </div>
              <div className="text-green-600">成功</div>
            </div>
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="text-2xl font-bold text-red-800">
                {testResults.filter(r => r.status === 'error').length}
              </div>
              <div className="text-red-600">失败</div>
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="text-2xl font-bold text-blue-800">
                {testResults.length}
              </div>
              <div className="text-blue-600">总计</div>
            </div>
          </div>
        </div>
      )}

      {/* 详细测试结果 */}
      <div className="space-y-4">
        {testResults.map((result, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold flex items-center">
                  {result.status === 'success' ? (
                    <span className="text-green-600 mr-2">✅</span>
                  ) : (
                    <span className="text-red-600 mr-2">❌</span>
                  )}
                  {result.name}
                </h3>
                <p className="text-sm text-gray-500">
                  {result.timestamp} • 耗时: {result.duration}ms
                </p>
              </div>
            </div>

            {result.status === 'success' && result.result && (
              <div className="space-y-3">
                <div className="bg-green-50 border border-green-200 rounded-md p-3">
                  <h4 className="font-medium text-green-800 mb-2">响应摘要</h4>
                  <div className="text-sm text-green-700 space-y-1">
                    <p>状态: {result.result.status}</p>
                    <p>消息: {result.result.message}</p>
                    {result.result.data && (
                      <>
                        <p>总数: {result.result.data.total}</p>
                        <p>当前页: {result.result.data.page}</p>
                        <p>每页大小: {result.result.data.size}</p>
                        <p>总页数: {result.result.data.pages}</p>
                        <p>返回条数: {result.result.data.requests?.length || 0}</p>
                      </>
                    )}
                  </div>
                </div>
                
                {result.result.data?.requests?.length > 0 && (
                  <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
                    <h4 className="font-medium text-gray-800 mb-2">示例数据</h4>
                    <div className="text-sm text-gray-700">
                      <p><strong>标题:</strong> {result.result.data.requests[0].title}</p>
                      <p><strong>状态:</strong> {result.result.data.requests[0].status}</p>
                      <p><strong>资源类型:</strong> {result.result.data.requests[0].resource_type}</p>
                      <p><strong>网盘类型:</strong> {result.result.data.requests[0].cloud_disk_types?.join(', ')}</p>
                    </div>
                  </div>
                )}

                <details className="bg-gray-50 border border-gray-200 rounded-md">
                  <summary className="p-3 cursor-pointer font-medium text-gray-800">
                    查看完整响应数据
                  </summary>
                  <pre className="p-3 text-xs bg-gray-100 overflow-auto max-h-96">
                    {JSON.stringify(result.result, null, 2)}
                  </pre>
                </details>
              </div>
            )}

            {result.status === 'error' && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <h4 className="font-medium text-red-800 mb-2">错误信息</h4>
                <p className="text-sm text-red-700">{result.error}</p>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* API文档链接 */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-6 mt-8">
        <h3 className="text-blue-800 font-medium mb-2">API文档</h3>
        <p className="text-sm text-blue-600 mb-3">
          完整的API文档请访问后端Swagger文档
        </p>
        <a
          href="http://127.0.0.1:9999/docs#/%E8%B5%84%E6%BA%90%E6%B1%82%E5%8A%A9/"
          target="_blank"
          rel="noopener noreferrer"
          className="inline-block px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          查看API文档
        </a>
      </div>
    </div>
  );
}
