# API路径修正报告

## 概述

根据实际的后端接口地址 `http://127.0.0.1:9999/api/help/requests`，对前端所有相关的API路径进行了修正。

## 实际后端接口地址

**正确的后端接口格式：**
```
http://127.0.0.1:9999/api/help/requests?page=1&size=20
```

**之前错误的路径：**
```
/api/help-requests
```

**修正后的路径：**
```
/api/help/requests
```

## 修正的文件

### 1. 服务层文件

**文件：** `src/services/helpRequestService.ts`

**修正的API路径：**
- `/api/help-requests` → `/api/help/requests`
- `/api/help-requests/${id}` → `/api/help/requests/${id}`
- `/api/help-requests/${id}/answers` → `/api/help/requests/${id}/answers`
- `/api/help-requests/${id}/adopt` → `/api/help/requests/${id}/adopt`
- `/api/user/help-requests` → `/api/user/help/requests`
- `/api/admin/help-requests` → `/api/admin/help/requests`
- `/api/admin/help-requests/${id}` → `/api/admin/help/requests/${id}`
- `/api/admin/help-requests/batch-delete` → `/api/admin/help/requests/batch-delete`
- `/api/admin/help-requests/stats` → `/api/admin/help/requests/stats`

### 2. API路由文件

**修正的文件：**
- `src/app/api/help-requests/route.ts`
- `src/app/api/help-requests/[id]/route.ts`
- `src/app/api/user/help-requests/route.ts`
- `src/app/api/admin/help-requests/route.ts`
- `src/app/api/admin/help-requests/[id]/route.ts`

**修正内容：**
所有转发到后端的请求路径都从 `/api/help-requests` 改为 `/api/help/requests`

### 3. 测试文件

**修正的文件：**
- `test-help-requests-api.js`
- `test-api-params.js`

**修正内容：**
更新所有测试用例中的API路径

### 4. 文档文件

**修正的文件：**
- `docs/help-requests-api-integration.md`

**修正内容：**
更新文档中所有的API路径示例和说明

## 完整的API路径映射

### 主要接口

| 前端路径 | 后端路径 | 描述 |
|----------|----------|------|
| `/api/help/requests` | `http://127.0.0.1:9999/api/help/requests` | 获取求助列表 |
| `/api/help/requests` | `http://127.0.0.1:9999/api/help/requests` | 创建求助 |
| `/api/help/requests/${id}` | `http://127.0.0.1:9999/api/help/requests/${id}` | 获取/更新/删除求助 |
| `/api/help/requests/${id}/answers` | `http://127.0.0.1:9999/api/help/requests/${id}/answers` | 创建回答 |
| `/api/help/requests/${id}/adopt` | `http://127.0.0.1:9999/api/help/requests/${id}/adopt` | 采纳答案 |

### 用户接口

| 前端路径 | 后端路径 | 描述 |
|----------|----------|------|
| `/api/user/help/requests` | `http://127.0.0.1:9999/api/user/help/requests` | 获取用户求助列表 |

### 管理员接口

| 前端路径 | 后端路径 | 描述 |
|----------|----------|------|
| `/api/admin/help/requests` | `http://127.0.0.1:9999/api/admin/help/requests` | 管理员获取求助列表 |
| `/api/admin/help/requests/${id}` | `http://127.0.0.1:9999/api/admin/help/requests/${id}` | 管理员删除求助 |
| `/api/admin/help/requests/batch-delete` | `http://127.0.0.1:9999/api/admin/help/requests/batch-delete` | 批量删除求助 |
| `/api/admin/help/requests/stats` | `http://127.0.0.1:9999/api/admin/help/requests/stats` | 获取求助统计 |

## 测试验证

### 1. 参数测试

运行参数测试脚本验证路径修正：
```bash
node test-api-params.js
```

### 2. 功能测试

运行功能测试脚本：
```bash
node test-help-requests-api.js
```

### 3. 可视化测试

访问测试页面：
```
http://localhost:3001/test-help-requests
```

## 环境配置

确保环境变量配置正确：

```env
# .env.local
NEXT_PUBLIC_API_BASE_URL=http://127.0.0.1:9999
```

## 验证清单

- [x] 服务层API路径修正
- [x] 前端API路由修正
- [x] 测试脚本路径修正
- [x] 文档路径更新
- [x] 环境变量配置
- [ ] 后端服务连接测试
- [ ] 完整功能测试

## 注意事项

1. **路径格式**：确保使用 `/api/help/requests` 而不是 `/api/help-requests`
2. **后端地址**：确认后端服务运行在 `http://127.0.0.1:9999`
3. **参数格式**：使用 `size` 而不是 `limit` 参数
4. **认证头**：需要认证的接口必须包含 `Authorization: Bearer token`

## 下一步

1. 启动后端服务（确保运行在 127.0.0.1:9999）
2. 运行前端开发服务器
3. 执行完整的API测试
4. 验证所有功能是否正常工作

现在所有的API路径都已经修正为正确的格式，可以与后端进行正常的联调测试了。
